import React from "react";
import { BrowserRouter as Router, Routes, Route, Navigate } from "react-router-dom";
import { Toaster } from 'react-hot-toast';
import './styles/layout.css';

// General Components
import Navbar from "./Components/Navbar.jsx";
import Hero from "./Components/Hero.jsx";
import AnimatedCardsSection from "./Components/AnimatedCardsSection.jsx";
import About from "./Components/About.jsx";
import HowItWorks from "./Components/HowItWorks.jsx";
import Testimonials from "./Components/Testimonials.jsx";
import Footer from "./Components/Footer.jsx";

// Layout
import Layout from "./Components/Layout.jsx";

// Protected Route Components
import ProtectedRoute from "./components/auth/ProtectedRoute.jsx";
import { AdminRoute, CompanyRoute, StudentRoute } from "./components/auth/RoleBasedRoute.jsx";

// Company Components
import CompanyDashboard from "./Components/company/CompanyDashboard.jsx";
import CreateJob from './Components/company/Createjob.jsx';
import TestManagement from "./Components/company/TestManagement.jsx";
import Aptitude from "./Components/company/Aptitude.jsx";
import Interview from "./Components/company/Interview.jsx";
import Profile from "./Components/company/Profile.jsx";

// Admin Components (Each has its own file)
import AdminDashboard from "./Components/admin/AdminDashboard.jsx";
import AdminJobPosts from "./Components/admin/AdminJobPosts.jsx";
import AdminCompanies from "./Components/admin/AdminCompanies.jsx";
import AdminUsers from "./Components/admin/AdminUsers.jsx";
import AdminSettings from "./Components/admin/AdminSettings.jsx";

// Student Components
import StudentDashboard from "./Components/Dashboard/Studentdashboard.jsx";
import Test from "./Components/company/components/TestInterface.jsx";
import TestResult from "./Components/Dashboard/Quiz.jsx"; // Using Quiz component for test results
import InterviewPrep from "./Components/Dashboard/interview.jsx";
import StudentProfile from "./Components/Dashboard/Profile.jsx";

// Demo Components
import AuthDemo from "./components/demo/AuthDemo.jsx";

// Auth Pages
import RegistrationPage from "./pages/RegistationPage.jsx";
import VerifyOtp from "./pages/VerifyOtp.jsx";
import LoginPage from "./pages/LoginPage.jsx";

// Landing Page
function LandingPage() {
  return (
    <div className="font-sans text-gray-800 bg-[#f7f8fa]">
      <Navbar />
      <main>
        <section className="min-h-[85vh] flex items-center justify-center bg-white">
          <Hero />
        </section>
        <section className="py-16 px-4 bg-white">
          <div className="max-w-7xl mx-auto bg-white">
            <AnimatedCardsSection />
          </div>
        </section>
        <section className="py-20 px-4 bg-white">
          <About />
        </section>
        <section className="py-20 px-4">
          <div className="max-w-7xl mx-auto">
            <HowItWorks />
          </div>
        </section>
        <section className="py-20 px-4">
          <Testimonials dark={false} />
        </section>
      </main>
      <Footer />
    </div>
  );
}

function App() {
  return (
    <Router>
      <Routes>
        {/* Public Routes */}
        <Route path="/" element={<LandingPage />} />

        {/* Demo Route - For testing authentication */}
        <Route path="/auth-demo" element={<AuthDemo />} />

        {/* Auth Routes - Redirect to dashboard if already logged in */}
        <Route
          path="/register"
          element={
            <ProtectedRoute requireAuth={false}>
              <RegistrationPage />
            </ProtectedRoute>
          }
        />
        <Route
          path="/verify-otp"
          element={
            <ProtectedRoute requireAuth={false}>
              <VerifyOtp />
            </ProtectedRoute>
          }
        />
        <Route
          path="/login"
          element={
            <ProtectedRoute requireAuth={false}>
              <LoginPage />
            </ProtectedRoute>
          }
        />

        {/* Admin Routes - Only accessible by admin role */}
        <Route
          path="/admin-dashboard"
          element={
            <AdminRoute>
              <AdminDashboard />
            </AdminRoute>
          }
        />
        <Route
          path="/admin-dashboard/job-posts"
          element={
            <AdminRoute>
              <AdminJobPosts />
            </AdminRoute>
          }
        />
        <Route
          path="/admin-dashboard/companies"
          element={
            <AdminRoute>
              <AdminCompanies />
            </AdminRoute>
          }
        />
        <Route
          path="/admin-dashboard/users"
          element={
            <AdminRoute>
              <AdminUsers />
            </AdminRoute>
          }
        />
        <Route
          path="/admin-dashboard/settings"
          element={
            <AdminRoute>
              <AdminSettings />
            </AdminRoute>
          }
        />

        {/* Company Routes under Layout - Only accessible by company role */}
        <Route
          path="/"
          element={
            <CompanyRoute>
              <Layout />
            </CompanyRoute>
          }
        >
          <Route path="dashboard" element={<CompanyDashboard />} />
          <Route path="job-create" element={<CreateJob />} />
          <Route path="test-management" element={<TestManagement />} />
          <Route path="aptitude" element={<Aptitude />} />
          <Route path="interview" element={<Interview />} />
          <Route path="profile" element={<Profile />} />
        </Route>

        {/* Student Routes - Only accessible by student role */}
        <Route
          path="/student-dashboard"
          element={
            <StudentRoute>
              <StudentDashboard />
            </StudentRoute>
          }
        />
        <Route
          path="/test"
          element={
            <StudentRoute>
              <Test />
            </StudentRoute>
          }
        />
        <Route
          path="/test-result"
          element={
            <StudentRoute>
              <TestResult />
            </StudentRoute>
          }
        />
        <Route
          path="/interview-prep"
          element={
            <StudentRoute>
              <InterviewPrep />
            </StudentRoute>
          }
        />
        <Route
          path="/student-profile"
          element={
            <StudentRoute>
              <StudentProfile />
            </StudentRoute>
          }
        />

        {/* Catch all route - redirect to home */}
        <Route
          path="*"
          element={<Navigate to="/" replace />}
        />
      </Routes>

      {/* Toast Notifications */}
      <Toaster
        position="top-right"
        toastOptions={{
          duration: 3000,
          style: {
            background: '#363636',
            color: '#fff',
            borderRadius: '10px',
            padding: '16px',
            fontSize: '14px',
            fontWeight: '500',
          },
          success: {
            style: { background: '#10B981' },
            iconTheme: { primary: '#fff', secondary: '#10B981' },
          },
          error: {
            style: { background: '#EF4444' },
            iconTheme: { primary: '#fff', secondary: '#EF4444' },
          },
          loading: {
            style: { background: '#3B82F6' },
          },
        }}
      />
    </Router>
  );
}

export default App;
